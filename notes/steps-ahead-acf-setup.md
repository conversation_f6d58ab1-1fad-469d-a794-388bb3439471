# Steps Ahead Panel - ACF Field Group Setup

## Overview
The Steps Ahead panel content is managed centrally in Theme Settings, with individual pages only controlling styling (background color, curved edge, and on/off status).

---

## 1. Theme Settings - Global Content

**Field Group Name:** Theme Settings  
**Location:** Options Page → Theme Settings  

### Add New Tab: "Steps Ahead Panel"
- **Field Type:** Tab
- **Field Label:** Steps Ahead Panel
- **Placement:** Left

### Steps Ahead Global Content (Group)
- **Field Type:** Group
- **Field Label:** Steps Ahead Global Content
- **Field Name:** `steps_ahead_global_content`
- **Instructions:** "This content will be used on all pages that have Steps Ahead panels enabled. Individual pages only control background color, curved edge, and on/off settings."

#### Sub-fields within the Group:

**Panel Title**
- **Field Type:** Text
- **Field Label:** Panel Title
- **Field Name:** `panel_title`
- **Default Value:** "Independently Owned, Personally Run"

**Panel Copy**
- **Field Type:** WYSIWYG Editor
- **Field Label:** Panel Copy
- **Field Name:** `panel_copy`
- **Instructions:** (leave blank)

**Hero Image**
- **Field Type:** Image
- **Field Label:** Hero Image
- **Field Name:** `hero_image`
- **Instructions:** "Large hero image that appears at the top of the panel"
- **Return Format:** Image Array

**Hero Title**
- **Field Type:** Text
- **Field Label:** Hero Title
- **Field Name:** `hero_title`
- **Instructions:** "Title that overlays the hero image"
- **Default Value:** "Independently Owned,<br>Personally Run"

**Content Columns**
- **Field Type:** Repeater
- **Field Label:** Content Columns
- **Field Name:** `columns`
- **Instructions:** "Add up to 4 columns for the steps ahead panel content"
- **Min Rows:** 0
- **Max Rows:** 4
- **Layout:** Block
- **Button Label:** "Add Column"

##### Sub-fields within Content Columns Repeater:

**Heading**
- **Field Type:** Text
- **Field Label:** Heading
- **Field Name:** `heading`

**Copy**
- **Field Type:** WYSIWYG Editor
- **Field Label:** Copy
- **Field Name:** `copy`

---

## 2. All Holidays Archive Page

**Field Group Name:** All Holidays Archive - Featured Holidays Panel  
**Location:** Post Type Archive → Holiday  

### Featured Holidays Panel (existing - keep as is)

### Steps Ahead Panel (Group)
- **Field Type:** Group
- **Field Label:** Steps Ahead Panel
- **Field Name:** `steps_ahead_panel`
- **Instructions:** "Content is managed centrally in Theme Settings. Only background color, curved edge, and on/off controls are available here."

#### Sub-fields:

**Enable Panel**
- **Field Type:** True/False
- **Field Label:** Enable Panel
- **Field Name:** `enable_panel`
- **Default Value:** Yes (checked)

**Background Color**
- **Field Type:** Color Picker
- **Field Label:** Background Color
- **Field Name:** `background_color`
- **Default Value:** `#ebf2f1`
- **Enable Opacity:** Yes

**Curved Edge**
- **Field Type:** Select
- **Field Label:** Curved Edge
- **Field Name:** `curved_edge`
- **Choices:**
  - `none` : None
  - `top` : Top
  - `bottom` : Bottom
- **Default Value:** `bottom`

---

## 3. Holiday Type Taxonomy Pages

**Field Group Name:** Holiday Type  
**Location:** Taxonomy Term → Holiday Type  

### Steps Ahead Panel (Group) - Same as All Holidays Archive
- **Field Type:** Group
- **Field Label:** Steps Ahead Panel
- **Field Name:** `steps_ahead_panel`
- **Instructions:** "Content is managed centrally in Theme Settings. Only background color, curved edge, and on/off controls are available here."

#### Sub-fields: (Same as All Holidays Archive)
- Enable Panel (True/False, default: Yes)
- Background Color (Color Picker, default: #ebf2f1)
- Curved Edge (Select, default: bottom)

---

## 4. Holiday Regions Taxonomy Pages

**Field Group Name:** Holiday Regions  
**Location:** Taxonomy Term → Holiday Regions  

### Steps Ahead Panel (Group) - Same as above
- **Field Type:** Group
- **Field Label:** Steps Ahead Panel
- **Field Name:** `steps_ahead_panel`
- **Instructions:** "Content is managed centrally in Theme Settings. Only background color, curved edge, and on/off controls are available here."

#### Sub-fields: (Same as All Holidays Archive)
- Enable Panel (True/False, default: Yes)
- Background Color (Color Picker, default: #ebf2f1)
- Curved Edge (Select, default: bottom)

---

## 5. Page Blocks (Home Page etc.)

**Field Group Name:** Page Blocks  
**Location:** Post Type → Page  

### Steps Ahead Block - Remove content fields, keep only:

**Background Color**
- **Field Type:** Color Picker
- **Field Label:** Background Color
- **Field Name:** `background_color`
- **Instructions:** "Choose a background color for the Steps Ahead block. Content is managed centrally in Theme Settings."
- **Default Value:** `#ebf2f1`

**Curved Edge**
- **Field Type:** Select
- **Field Label:** Curved Edge
- **Field Name:** `curved_edge`
- **Choices:**
  - `none` : None
  - `top` : Top
  - `bottom` : Bottom
- **Default Value:** `bottom`

---

## Summary

- **Theme Settings:** Global content with columns repeater
- **All other locations:** Only styling controls (enable, background color, curved edge)
- **Default values:** Active, #ebf2f1 background, bottom curve
