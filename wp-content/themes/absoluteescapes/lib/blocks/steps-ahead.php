<?php

/**
 * Steps Ahead
 * Content comes from Theme Settings, individual blocks control styling only
 */

// Get global content from Theme Settings
$global_content = get_field('steps_ahead_global_content', 'option');
if (!$global_content) {
    return;
}

// Use global content for title, copy, and columns
$heading = $global_content['panel_title'] ?: '';
$copy = $global_content['panel_copy'] ?: '';
$hero_image = $global_content['hero_image'] ?: null;
$hero_title = $global_content['hero_title'] ?: '';
$columns = $global_content['columns'];

// Use individual block settings for styling
$background_color = get_sub_field('background_color');
$curved_edge = get_sub_field('curved_edge');

// Set up background classes
$background_classes = '';
if ($background_color) {
    $background_classes .= ' has-background';
}
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
}

?>

<section class="steps-ahead">
    <?php if($hero_image) : ?>
        <div class="steps-ahead__hero">
            <div class="steps-ahead__hero-image">
                <img src="<?php echo $hero_image['url']; ?>" alt="<?php echo $hero_image['alt']; ?>">
            </div>
            <?php if($hero_title) : ?>
                <div class="steps-ahead__hero-content">
                    <h2 class="steps-ahead__hero-title"><?php echo $hero_title; ?></h2>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="steps-ahead__inner<?php echo $background_classes; ?>" data-aos="fade"<?php if ($background_color) : ?> style="background-color: <?php echo $background_color; ?>;"<?php endif; ?>>
        <div class="container steps-ahead__container">
            <?php if($heading && !$hero_image) : ?>
                <div class="steps-ahead__content centre inner-container">
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                    <?php if($copy) : ?>
                        <div class="content-area steps-ahead__copy">
                            <?php echo $copy; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if($columns && !empty($columns)) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($columns as $column) : ?>
                        <?php
                        $cheading = $column['heading'];
                        $ccopy = $column['copy'];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre">
                                <?php if($cheading) : ?>
                                    <h4 class="steps-ahead__col-heading"><?php echo esc_html($cheading); ?></h4>
                                <?php endif; ?>
                                <?php if($ccopy) : ?>
                                    <div class="steps-ahead__col-copy content-area">
                                        <?php echo $ccopy; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->