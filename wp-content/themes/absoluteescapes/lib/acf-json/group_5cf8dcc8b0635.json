{
    "key": "group_5cf8dcc8b0635",
    "title": "Theme Settings",
    "fields": [
        {
            "key": "field_5cf9125ce22a0",
            "label": "Header",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5cf91283e22a1",
            "label": "Logo",
            "name": "logo",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "return_format": "array",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5d0a1229c5714",
            "label": "Logo Mobile",
            "name": "logo_mobile",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "return_format": "array",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5cfe2c0ddfb26",
            "label": "Review Embed",
            "name": "review_embed",
            "aria-label": "",
            "type": "textarea",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "maxlength": "",
            "rows": "",
            "new_lines": ""
        },
        {
            "key": "field_5cfe2c1bdfb27",
            "label": "Review Embed Fallback",
            "name": "review_embed_fallback",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "return_format": "array",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5cfe2a6e10bdf",
            "label": "Image",
            "name": "blog_image",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "id",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5cf912ace22a2",
            "label": "Megamenu",
            "name": "megamenu",
            "aria-label": "",
            "type": "repeater",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "collapsed": "",
            "min": 0,
            "max": 0,
            "layout": "block",
            "button_label": "Add Row",
            "rows_per_page": 20,
            "sub_fields": [
                {
                    "key": "field_5cf912c0e22a3",
                    "label": "Link",
                    "name": "link",
                    "aria-label": "",
                    "type": "link",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "parent_repeater": "field_5cf912ace22a2"
                },
                {
                    "key": "field_5cf912e4e22a4",
                    "label": "Has Sub Links",
                    "name": "has_sub_links",
                    "aria-label": "",
                    "type": "true_false",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "message": "",
                    "default_value": 0,
                    "ui": 0,
                    "ui_on_text": "",
                    "ui_off_text": "",
                    "parent_repeater": "field_5cf912ace22a2"
                },
                {
                    "key": "field_5cf91325e22a5",
                    "label": "Sub Links",
                    "name": "sub_links",
                    "aria-label": "",
                    "type": "repeater",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_5cf912e4e22a4",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "collapsed": "",
                    "min": 0,
                    "max": 0,
                    "layout": "block",
                    "button_label": "Add Row",
                    "rows_per_page": 20,
                    "parent_repeater": "field_5cf912ace22a2",
                    "sub_fields": [
                        {
                            "key": "field_5cf9133ae22a6",
                            "label": "Link",
                            "name": "link",
                            "aria-label": "",
                            "type": "link",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "array",
                            "parent_repeater": "field_5cf91325e22a5"
                        },
                        {
                            "key": "field_5cf91362e22a8",
                            "label": "Image",
                            "name": "image",
                            "aria-label": "",
                            "type": "image",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "id",
                            "preview_size": "medium",
                            "library": "all",
                            "min_width": "",
                            "min_height": "",
                            "min_size": "",
                            "max_width": "",
                            "max_height": "",
                            "max_size": "",
                            "mime_types": "",
                            "parent_repeater": "field_5cf91325e22a5"
                        },
                        {
                            "key": "field_5cf91347e22a7",
                            "label": "Has Sub Links",
                            "name": "has_sub_links",
                            "aria-label": "",
                            "type": "true_false",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "message": "",
                            "default_value": 0,
                            "ui": 0,
                            "ui_on_text": "",
                            "ui_off_text": "",
                            "parent_repeater": "field_5cf91325e22a5"
                        },
                        {
                            "key": "field_5cf91371e22a9",
                            "label": "Sub Links",
                            "name": "sub_links",
                            "aria-label": "",
                            "type": "repeater",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": [
                                [
                                    {
                                        "field": "field_5cf91347e22a7",
                                        "operator": "==",
                                        "value": "1"
                                    }
                                ]
                            ],
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "collapsed": "",
                            "min": 0,
                            "max": 0,
                            "layout": "table",
                            "button_label": "Add Row",
                            "rows_per_page": 20,
                            "parent_repeater": "field_5cf91325e22a5",
                            "sub_fields": [
                                {
                                    "key": "field_5cf91494e22b1",
                                    "label": "Links Heading",
                                    "name": "links_heading",
                                    "aria-label": "",
                                    "type": "text",
                                    "instructions": "",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "default_value": "",
                                    "placeholder": "",
                                    "prepend": "",
                                    "append": "",
                                    "maxlength": "",
                                    "parent_repeater": "field_5cf91371e22a9"
                                },
                                {
                                    "key": "field_5cf91476e22af",
                                    "label": "Links",
                                    "name": "links",
                                    "aria-label": "",
                                    "type": "repeater",
                                    "instructions": "",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "collapsed": "",
                                    "min": 0,
                                    "max": 0,
                                    "layout": "table",
                                    "button_label": "Add Row",
                                    "rows_per_page": 20,
                                    "parent_repeater": "field_5cf91371e22a9",
                                    "sub_fields": [
                                        {
                                            "key": "field_5cf9147ee22b0",
                                            "label": "Link",
                                            "name": "link",
                                            "aria-label": "",
                                            "type": "link",
                                            "instructions": "",
                                            "required": 0,
                                            "conditional_logic": 0,
                                            "wrapper": {
                                                "width": "",
                                                "class": "",
                                                "id": ""
                                            },
                                            "return_format": "array",
                                            "parent_repeater": "field_5cf91476e22af"
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "key": "field_5cfa4998fed93",
            "label": "Footer",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5cfa49f1fed97",
            "label": "Logo",
            "name": "footer_logo",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "array",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5e593b2e38de6",
            "label": "Footer Logos",
            "name": "footer_logos",
            "aria-label": "",
            "type": "repeater",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "collapsed": "",
            "min": 0,
            "max": 0,
            "layout": "table",
            "button_label": "Add Row",
            "rows_per_page": 20,
            "sub_fields": [
                {
                    "key": "field_5e593b3538de7",
                    "label": "Logo",
                    "name": "logo",
                    "aria-label": "",
                    "type": "image",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "preview_size": "medium",
                    "library": "all",
                    "min_width": "",
                    "min_height": "",
                    "min_size": "",
                    "max_width": "",
                    "max_height": "",
                    "max_size": "",
                    "mime_types": "",
                    "parent_repeater": "field_5e593b2e38de6"
                },
                {
                    "key": "field_5e861ab6b9541",
                    "label": "Link",
                    "name": "link",
                    "aria-label": "",
                    "type": "link",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "parent_repeater": "field_5e593b2e38de6"
                }
            ]
        },
        {
            "key": "field_5cfa49adfed94",
            "label": "Form Heading",
            "name": "footer_form_heading",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5cfa49cdfed96",
            "label": "Form ID",
            "name": "footer_form_id",
            "aria-label": "",
            "type": "number",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "min": "",
            "max": "",
            "step": ""
        },
        {
            "key": "field_5cfa49bbfed95",
            "label": "Form Description",
            "name": "footer_form_description",
            "aria-label": "",
            "type": "textarea",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "maxlength": "",
            "rows": "",
            "new_lines": ""
        },
        {
            "key": "field_5cfa4a25fed98",
            "label": "Agency Text",
            "name": "agency_text",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5cfa4a30fed99",
            "label": "Agency Link",
            "name": "agency_link",
            "aria-label": "",
            "type": "link",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "return_format": "array"
        },
        {
            "key": "field_5cfa4a52df57c",
            "label": "Agency Logo",
            "name": "agency_logo",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "array",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5cf8dcda0525b",
            "label": "Social",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5cf8dda205260",
            "label": "Social Links",
            "name": "social_links",
            "aria-label": "",
            "type": "group",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layout": "block",
            "sub_fields": [
                {
                    "key": "field_5cf8ddc205261",
                    "label": "Instagram",
                    "name": "instagram",
                    "aria-label": "",
                    "type": "group",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "layout": "block",
                    "sub_fields": [
                        {
                            "key": "field_5cf8ddce05262",
                            "label": "Link",
                            "name": "link",
                            "aria-label": "",
                            "type": "link",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": ""
                        },
                        {
                            "key": "field_5cf8ddec05264",
                            "label": "Icon",
                            "name": "icon",
                            "aria-label": "",
                            "type": "image",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "url",
                            "preview_size": "thumbnail",
                            "library": "all",
                            "min_width": "",
                            "min_height": "",
                            "min_size": "",
                            "max_width": "",
                            "max_height": "",
                            "max_size": "",
                            "mime_types": ""
                        },
                        {
                            "key": "field_5cf8ddd805263",
                            "label": "Icon Font Awesome",
                            "name": "icon_fa",
                            "aria-label": "",
                            "type": "text",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "placeholder": "",
                            "prepend": "",
                            "append": "",
                            "maxlength": ""
                        }
                    ]
                },
                {
                    "key": "field_5cf8de4b05268",
                    "label": "Twitter",
                    "name": "twitter",
                    "aria-label": "",
                    "type": "group",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "layout": "block",
                    "sub_fields": [
                        {
                            "key": "field_5cf8de4b05269",
                            "label": "Link",
                            "name": "link",
                            "aria-label": "",
                            "type": "link",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": ""
                        },
                        {
                            "key": "field_5cf8de4b0526b",
                            "label": "Icon",
                            "name": "icon",
                            "aria-label": "",
                            "type": "image",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "url",
                            "preview_size": "thumbnail",
                            "library": "",
                            "min_width": "",
                            "min_height": "",
                            "min_size": "",
                            "max_width": "",
                            "max_height": "",
                            "max_size": "",
                            "mime_types": ""
                        },
                        {
                            "key": "field_5cf8de4b0526c",
                            "label": "Icon Font Awesome",
                            "name": "icon_fa",
                            "aria-label": "",
                            "type": "text",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "placeholder": "",
                            "prepend": "",
                            "append": "",
                            "maxlength": ""
                        }
                    ]
                },
                {
                    "key": "field_5cf8de5a0526e",
                    "label": "Facebook",
                    "name": "facebook",
                    "aria-label": "",
                    "type": "group",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "layout": "block",
                    "sub_fields": [
                        {
                            "key": "field_5cf8de5a0526f",
                            "label": "Link",
                            "name": "link",
                            "aria-label": "",
                            "type": "link",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": ""
                        },
                        {
                            "key": "field_5cf8de5a05271",
                            "label": "Icon",
                            "name": "icon",
                            "aria-label": "",
                            "type": "image",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "url",
                            "preview_size": "thumbnail",
                            "library": "all",
                            "min_width": "",
                            "min_height": "",
                            "min_size": "",
                            "max_width": "",
                            "max_height": "",
                            "max_size": "",
                            "mime_types": ""
                        },
                        {
                            "key": "field_5cf8de5a05272",
                            "label": "Icon Font Awesome",
                            "name": "icon_fa",
                            "aria-label": "",
                            "type": "text",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "placeholder": "",
                            "prepend": "",
                            "append": "",
                            "maxlength": ""
                        }
                    ]
                },
                {
                    "key": "field_5cf8de6605273",
                    "label": "Linkedin",
                    "name": "linkedin",
                    "aria-label": "",
                    "type": "group",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "layout": "block",
                    "sub_fields": [
                        {
                            "key": "field_5cf8de6605274",
                            "label": "Link",
                            "name": "link",
                            "aria-label": "",
                            "type": "link",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "array"
                        },
                        {
                            "key": "field_5cf8de6605276",
                            "label": "Icon",
                            "name": "icon",
                            "aria-label": "",
                            "type": "image",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "return_format": "url",
                            "preview_size": "thumbnail",
                            "library": "all",
                            "min_width": "",
                            "min_height": "",
                            "min_size": "",
                            "max_width": "",
                            "max_height": "",
                            "max_size": "",
                            "mime_types": ""
                        },
                        {
                            "key": "field_5cf8de6605277",
                            "label": "Icon Font Awesome",
                            "name": "icon_fa",
                            "aria-label": "",
                            "type": "text",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "50",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "placeholder": "",
                            "prepend": "",
                            "append": "",
                            "maxlength": ""
                        }
                    ]
                }
            ]
        },
        {
            "key": "field_5cf8dd260525c",
            "label": "Instagram Block",
            "name": "instagram_block",
            "aria-label": "",
            "type": "group",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layout": "block",
            "sub_fields": [
                {
                    "key": "field_5cf8dd370525d",
                    "label": "Heading",
                    "name": "heading",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": "",
                    "maxlength": ""
                },
                {
                    "key": "field_5cf8dd460525e",
                    "label": "Copy",
                    "name": "copy",
                    "aria-label": "",
                    "type": "wysiwyg",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "tabs": "all",
                    "toolbar": "full",
                    "media_upload": 1,
                    "delay": 0
                },
                {
                    "key": "field_5cf8de2405266",
                    "label": "Links Heading",
                    "name": "links_heading",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": "",
                    "maxlength": ""
                }
            ]
        },
        {
            "key": "field_5cfa3b28f4372",
            "label": "Contact Details",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5cfa3b3ec04ea",
            "label": "Tel",
            "name": "tel",
            "aria-label": "",
            "type": "link",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "return_format": "array"
        },
        {
            "key": "field_5cfa3b46c04eb",
            "label": "Contact Link",
            "name": "contact_link",
            "aria-label": "",
            "type": "link",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "return_format": "array"
        },
        {
            "key": "field_5cfe29dc10bdb",
            "label": "Blog",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5cfe2a4410bdc",
            "label": "Heading",
            "name": "blog_heading",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5cfe2a4d10bdd",
            "label": "Subheading",
            "name": "blog_subheading",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5cfe505abe67c",
            "label": "Background",
            "name": "blog_background",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "id",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_675c0c8614262",
            "label": "Sidebar CTA",
            "name": "sidebar_cta",
            "aria-label": "",
            "type": "group",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layout": "block",
            "sub_fields": [
                {
                    "key": "field_675c10d9a058e",
                    "label": "Status",
                    "name": "status",
                    "aria-label": "",
                    "type": "true_false",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "message": "Active",
                    "default_value": 1,
                    "ui": 0,
                    "ui_on_text": "",
                    "ui_off_text": ""
                },
                {
                    "key": "field_675c0fb4a0588",
                    "label": "Title",
                    "name": "title",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "Talk to our travel experts",
                    "maxlength": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                },
                {
                    "key": "field_675c0fbaa0589",
                    "label": "Introduction",
                    "name": "introduction",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "First-hand knowledge, tailored to you. Get in touch today to plan your perfect escape.",
                    "maxlength": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                },
                {
                    "key": "field_675c13ea59498",
                    "label": "Button label",
                    "name": "button_label",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "Make an Enquiry",
                    "maxlength": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                },
                {
                    "key": "field_675c140859499",
                    "label": "Button link",
                    "name": "button_link",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "50",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "\/general-enquiry\/",
                    "maxlength": "",
                    "allow_in_bindings": 1,
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                },
                {
                    "key": "field_675c0fc1a058b",
                    "label": "Supporting text",
                    "name": "supporting_text",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "Prefer to chat? Our welcoming, knowledgeable team are just a phone call away.",
                    "maxlength": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                },
                {
                    "key": "field_675c11e5524f1",
                    "label": "Icon",
                    "name": "icon",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "HTML\/SVG only.",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "<svg class=\"svg-inline--fa fa-phone fa-w-16\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fas\" data-icon=\"phone\" role=\"img\" xmlns=\"http:\/\/www.w3.org\/2000\/svg\" viewBox=\"0 0 512 512\" data-fa-i2svg=\"\"><path fill=\"currentColor\" d=\"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z\"><\/path><\/svg>",
                    "maxlength": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                },
                {
                    "key": "field_675c0fc7a058c",
                    "label": "Contact number",
                    "name": "contact_number",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": [
                        [
                            {
                                "field": "field_675c10d9a058e",
                                "operator": "==",
                                "value": "1"
                            }
                        ]
                    ],
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "+44 (0) 131 610 1210",
                    "maxlength": "",
                    "placeholder": "",
                    "prepend": "",
                    "append": ""
                }
            ]
        },
        {
            "key": "field_5d00b6b83c0df",
            "label": "Keys",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5d00b6fc3c0e0",
            "label": "Google Maps API Key",
            "name": "google_maps_api_key",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d07a90ce5bbf",
            "label": "Forms",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5d07a919e5bc0",
            "label": "Holiday Form",
            "name": "holiday_form",
            "aria-label": "",
            "type": "group",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layout": "block",
            "sub_fields": [
                {
                    "key": "field_5d07a928e5bc1",
                    "label": "Footer Text",
                    "name": "footer_text",
                    "aria-label": "",
                    "type": "wysiwyg",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "tabs": "all",
                    "toolbar": "full",
                    "media_upload": 1,
                    "delay": 0
                },
                {
                    "key": "field_5d07a961e5bc2",
                    "label": "Review Embed",
                    "name": "review_embed",
                    "aria-label": "",
                    "type": "textarea",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "placeholder": "",
                    "maxlength": "",
                    "rows": "",
                    "new_lines": ""
                },
                {
                    "key": "field_5d07a972e5bc3",
                    "label": "Review Embed (Fallback)",
                    "name": "review_embed_fallback",
                    "aria-label": "",
                    "type": "image",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "preview_size": "medium",
                    "library": "all",
                    "min_width": "",
                    "min_height": "",
                    "min_size": "",
                    "max_width": "",
                    "max_height": "",
                    "max_size": "",
                    "mime_types": ""
                },
                {
                    "key": "field_5d0b5c960a543",
                    "label": "Review Embed Mobile",
                    "name": "review_embed_mobile",
                    "aria-label": "",
                    "type": "textarea",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "placeholder": "",
                    "maxlength": "",
                    "rows": "",
                    "new_lines": ""
                },
                {
                    "key": "field_5d0b5ca00a545",
                    "label": "Review Embed Mobile (Fallback)",
                    "name": "review_embed_mobile_fallback",
                    "aria-label": "",
                    "type": "image",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "preview_size": "medium",
                    "library": "all",
                    "min_width": "",
                    "min_height": "",
                    "min_size": "",
                    "max_width": "",
                    "max_height": "",
                    "max_size": "",
                    "mime_types": ""
                }
            ]
        },
        {
            "key": "field_5d08a6868f59a",
            "label": "Enquiry Form Details",
            "name": "enquiry_form_details",
            "aria-label": "",
            "type": "group",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layout": "block",
            "sub_fields": [
                {
                    "key": "field_5d08aa1983656",
                    "label": "Points",
                    "name": "points",
                    "aria-label": "",
                    "type": "repeater",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "collapsed": "",
                    "min": 0,
                    "max": 3,
                    "layout": "table",
                    "button_label": "Add Row",
                    "rows_per_page": 20,
                    "sub_fields": [
                        {
                            "key": "field_5d08aa2483657",
                            "label": "Point",
                            "name": "point",
                            "aria-label": "",
                            "type": "text",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "placeholder": "",
                            "prepend": "",
                            "append": "",
                            "maxlength": "",
                            "parent_repeater": "field_5d08aa1983656"
                        }
                    ]
                }
            ]
        },
        {
            "key": "field_5d0b8e9ce07f4",
            "label": "404",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5d0b8ea7e07f5",
            "label": "Heading",
            "name": "404_heading",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d0b8eafe07f6",
            "label": "Copy",
            "name": "404_copy",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d0b90a4d6465",
            "label": "Background",
            "name": "404_background",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "id",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5d0b8ecde07f7",
            "label": "Links",
            "name": "404_links",
            "aria-label": "",
            "type": "repeater",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "collapsed": "",
            "min": 0,
            "max": 0,
            "layout": "table",
            "button_label": "Add Row",
            "rows_per_page": 20,
            "sub_fields": [
                {
                    "key": "field_5d0b8ed7e07f8",
                    "label": "Link",
                    "name": "link",
                    "aria-label": "",
                    "type": "link",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "parent_repeater": "field_5d0b8ecde07f7"
                }
            ]
        },
        {
            "key": "field_5d0b8ee2e07f9",
            "label": "No Content",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5d0b8ef0e07fa",
            "label": "Heading",
            "name": "no_content_heading",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d0b8f12e07fb",
            "label": "Subheading",
            "name": "no_content_subheading",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d0b9083d6464",
            "label": "Background",
            "name": "no_content_background",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "id",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5d0b8f1fe07fc",
            "label": "Links",
            "name": "no_content_links",
            "aria-label": "",
            "type": "repeater",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "collapsed": "",
            "min": 0,
            "max": 0,
            "layout": "table",
            "button_label": "Add Row",
            "rows_per_page": 20,
            "sub_fields": [
                {
                    "key": "field_5d0b8f2ae07fd",
                    "label": "Link",
                    "name": "link",
                    "aria-label": "",
                    "type": "link",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "parent_repeater": "field_5d0b8f1fe07fc"
                }
            ]
        },
        {
            "key": "field_5d0b9cebdf51a",
            "label": "Holidays Archive",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5d0b9cf9df51b",
            "label": "Holiday Archive Background",
            "name": "holiday_archive_background",
            "aria-label": "",
            "type": "image",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "return_format": "id",
            "preview_size": "medium",
            "library": "all",
            "min_width": "",
            "min_height": "",
            "min_size": "",
            "max_width": "",
            "max_height": "",
            "max_size": "",
            "mime_types": ""
        },
        {
            "key": "field_5d650244b9ba6",
            "label": "Misc",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_5d65024db9ba7",
            "label": "Difficulty Level 1",
            "name": "difficulty_level_1",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d65026eb9bab",
            "label": "Difficulty Level 2",
            "name": "difficulty_level_2",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d650271b9bac",
            "label": "Difficulty Level 3",
            "name": "difficulty_level_3",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_5d650272b9bad",
            "label": "Difficulty Level 4",
            "name": "difficulty_level_4",
            "aria-label": "",
            "type": "text",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "50",
                "class": "",
                "id": ""
            },
            "default_value": "",
            "placeholder": "",
            "prepend": "",
            "append": "",
            "maxlength": ""
        },
        {
            "key": "field_637e59986bf85",
            "label": "A\/B Testing",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "top",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_637e59986bf85",
            "label": "A\/B Testing",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "top",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_637e59aa6bf86",
            "label": "Test Group",
            "name": "test_group",
            "aria-label": "",
            "type": "flexible_content",
            "instructions": "Define the content (of a single type) to appear within the test group. Don't mix and match content types within a test group.",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layouts": {
                "layout_637e59c899dcb": {
                    "key": "layout_637e59c899dcb",
                    "name": "form_modal",
                    "label": "Form Modal",
                    "display": "block",
                    "sub_fields": [
                        {
                            "key": "field_637e60d12a313",
                            "label": "Enabled",
                            "name": "enabled",
                            "aria-label": "",
                            "type": "true_false",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "message": "",
                            "default_value": 0,
                            "ui": 1,
                            "ui_on_text": "",
                            "ui_off_text": ""
                        },
                        {
                            "key": "field_637e59d26bf87",
                            "label": "Form Modals",
                            "name": "Form Modal",
                            "aria-label": "",
                            "type": "repeater",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "collapsed": "",
                            "min": 0,
                            "max": 0,
                            "layout": "block",
                            "button_label": "Add Content to Test Group",
                            "rows_per_page": 20,
                            "sub_fields": [
                                {
                                    "key": "field_637e5a126bf89",
                                    "label": "Layout",
                                    "name": "layout",
                                    "aria-label": "",
                                    "type": "radio",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "choices": {
                                        "one-column": "Single column (image as background)",
                                        "two-column": "Two columns (image on left)"
                                    },
                                    "allow_null": 0,
                                    "other_choice": 0,
                                    "default_value": "",
                                    "layout": "horizontal",
                                    "return_format": "value",
                                    "save_other_choice": 0,
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e59ff6bf88",
                                    "label": "Content",
                                    "name": "content",
                                    "aria-label": "",
                                    "type": "wysiwyg",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "default_value": "",
                                    "tabs": "all",
                                    "toolbar": "full",
                                    "media_upload": 1,
                                    "delay": 0,
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e5a776bf8a",
                                    "label": "Image",
                                    "name": "image",
                                    "aria-label": "",
                                    "type": "image",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "return_format": "array",
                                    "preview_size": "medium",
                                    "library": "all",
                                    "min_width": "",
                                    "min_height": "",
                                    "min_size": "",
                                    "max_width": "",
                                    "max_height": "",
                                    "max_size": "",
                                    "mime_types": "",
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637f8e9abf1ed",
                                    "label": "Hover image",
                                    "name": "hover_image",
                                    "aria-label": "",
                                    "type": "image",
                                    "instructions": "Image that appears when modal is hovered. Only applies to the one column layout.",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "return_format": "array",
                                    "preview_size": "medium",
                                    "library": "all",
                                    "min_width": "",
                                    "min_height": "",
                                    "min_size": "",
                                    "max_width": "",
                                    "max_height": "",
                                    "max_size": "",
                                    "mime_types": "",
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e5a9a6bf8b",
                                    "label": "Form",
                                    "name": "gravity_forms",
                                    "aria-label": "",
                                    "type": "select",
                                    "instructions": "",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "multiple": 0,
                                    "allow_null": 0,
                                    "choices": {
                                        "1": "Footer Newsletter",
                                        "5": "Make an Enquiry - General",
                                        "2": "Make an Enquiry - Specific Holiday",
                                        "7": "Newsletter modal form A",
                                        "8": "Newsletter modal form B (switched off in June 2023)",
                                        "9": "Newsletter signup - landing page",
                                        "3": "Payment Form"
                                    },
                                    "default_value": false,
                                    "ui": 0,
                                    "ajax": 0,
                                    "placeholder": "",
                                    "return_format": "value",
                                    "parent_repeater": "field_637e59d26bf87"
                                }
                            ]
                        }
                    ],
                    "min": "",
                    "max": ""
                }
            },
            "button_label": "Add Content",
            "min": "",
            "max": ""
        },
        {
            "key": "field_637e59aa6bf86",
            "label": "Test Group",
            "name": "test_group",
            "aria-label": "",
            "type": "flexible_content",
            "instructions": "Define the content (of a single type) to appear within the test group. Don't mix and match content types within a test group.",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layouts": {
                "layout_637e59c899dcb": {
                    "key": "layout_637e59c899dcb",
                    "name": "form_modal",
                    "label": "Form Modal",
                    "display": "block",
                    "sub_fields": [
                        {
                            "key": "field_637e60d12a313",
                            "label": "Enabled",
                            "name": "enabled",
                            "aria-label": "",
                            "type": "true_false",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "message": "",
                            "default_value": 0,
                            "ui": 1,
                            "ui_on_text": "",
                            "ui_off_text": ""
                        },
                        {
                            "key": "field_637e59d26bf87",
                            "label": "Form Modals",
                            "name": "Form Modal",
                            "aria-label": "",
                            "type": "repeater",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "collapsed": "",
                            "min": 0,
                            "max": 0,
                            "layout": "block",
                            "button_label": "Add Content to Test Group",
                            "rows_per_page": 20,
                            "sub_fields": [
                                {
                                    "key": "field_637e5a126bf89",
                                    "label": "Layout",
                                    "name": "layout",
                                    "aria-label": "",
                                    "type": "radio",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "choices": {
                                        "one-column": "Single column (image as background)",
                                        "two-column": "Two columns (image on left)"
                                    },
                                    "allow_null": 0,
                                    "other_choice": 0,
                                    "default_value": "",
                                    "layout": "horizontal",
                                    "return_format": "value",
                                    "save_other_choice": 0,
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e59ff6bf88",
                                    "label": "Content",
                                    "name": "content",
                                    "aria-label": "",
                                    "type": "wysiwyg",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "default_value": "",
                                    "tabs": "all",
                                    "toolbar": "full",
                                    "media_upload": 1,
                                    "delay": 0,
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e5a776bf8a",
                                    "label": "Image",
                                    "name": "image",
                                    "aria-label": "",
                                    "type": "image",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "return_format": "array",
                                    "preview_size": "medium",
                                    "library": "all",
                                    "min_width": "",
                                    "min_height": "",
                                    "min_size": "",
                                    "max_width": "",
                                    "max_height": "",
                                    "max_size": "",
                                    "mime_types": "",
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637f8e9abf1ed",
                                    "label": "Hover image",
                                    "name": "hover_image",
                                    "aria-label": "",
                                    "type": "image",
                                    "instructions": "Image that appears when modal is hovered. Only applies to the one column layout.",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "return_format": "array",
                                    "preview_size": "medium",
                                    "library": "all",
                                    "min_width": "",
                                    "min_height": "",
                                    "min_size": "",
                                    "max_width": "",
                                    "max_height": "",
                                    "max_size": "",
                                    "mime_types": "",
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e5a9a6bf8b",
                                    "label": "Form",
                                    "name": "gravity_forms",
                                    "aria-label": "",
                                    "type": "select",
                                    "instructions": "",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "multiple": 0,
                                    "allow_null": 0,
                                    "choices": {
                                        "1": "Footer Newsletter",
                                        "5": "Make an Enquiry - General",
                                        "2": "Make an Enquiry - Specific Holiday",
                                        "7": "Newsletter modal form A",
                                        "8": "Newsletter modal form B (switched off in June 2023)",
                                        "9": "Newsletter signup - landing page",
                                        "3": "Payment Form"
                                    },
                                    "default_value": false,
                                    "ui": 0,
                                    "ajax": 0,
                                    "placeholder": "",
                                    "return_format": "value",
                                    "parent_repeater": "field_637e59d26bf87"
                                }
                            ]
                        }
                    ],
                    "min": "",
                    "max": ""
                }
            },
            "button_label": "Add Content",
            "min": "",
            "max": ""
        },
        {
            "key": "field_637e59986bf85",
            "label": "A\/B Testing",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "top",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_637e59aa6bf86",
            "label": "Test Group",
            "name": "test_group",
            "aria-label": "",
            "type": "flexible_content",
            "instructions": "Define the content (of a single type) to appear within the test group. Don't mix and match content types within a test group.",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layouts": {
                "layout_637e59c899dcb": {
                    "key": "layout_637e59c899dcb",
                    "name": "form_modal",
                    "label": "Form Modal",
                    "display": "block",
                    "sub_fields": [
                        {
                            "key": "field_637e60d12a313",
                            "label": "Enabled",
                            "name": "enabled",
                            "aria-label": "",
                            "type": "true_false",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "message": "",
                            "default_value": 0,
                            "ui": 1,
                            "ui_on_text": "",
                            "ui_off_text": ""
                        },
                        {
                            "key": "field_637e59d26bf87",
                            "label": "Form Modals",
                            "name": "Form Modal",
                            "aria-label": "",
                            "type": "repeater",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "collapsed": "",
                            "min": 0,
                            "max": 0,
                            "layout": "block",
                            "button_label": "Add Content to Test Group",
                            "rows_per_page": 20,
                            "sub_fields": [
                                {
                                    "key": "field_637e5a126bf89",
                                    "label": "Layout",
                                    "name": "layout",
                                    "aria-label": "",
                                    "type": "radio",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "choices": {
                                        "one-column": "Single column (image as background)",
                                        "two-column": "Two columns (image on left)"
                                    },
                                    "allow_null": 0,
                                    "other_choice": 0,
                                    "default_value": "",
                                    "layout": "horizontal",
                                    "return_format": "value",
                                    "save_other_choice": 0,
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e59ff6bf88",
                                    "label": "Content",
                                    "name": "content",
                                    "aria-label": "",
                                    "type": "wysiwyg",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "default_value": "",
                                    "tabs": "all",
                                    "toolbar": "full",
                                    "media_upload": 1,
                                    "delay": 0,
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e5a776bf8a",
                                    "label": "Image",
                                    "name": "image",
                                    "aria-label": "",
                                    "type": "image",
                                    "instructions": "",
                                    "required": 1,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "return_format": "array",
                                    "preview_size": "medium",
                                    "library": "all",
                                    "min_width": "",
                                    "min_height": "",
                                    "min_size": "",
                                    "max_width": "",
                                    "max_height": "",
                                    "max_size": "",
                                    "mime_types": "",
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637f8e9abf1ed",
                                    "label": "Hover image",
                                    "name": "hover_image",
                                    "aria-label": "",
                                    "type": "image",
                                    "instructions": "Image that appears when modal is hovered. Only applies to the one column layout.",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "return_format": "array",
                                    "preview_size": "medium",
                                    "library": "all",
                                    "min_width": "",
                                    "min_height": "",
                                    "min_size": "",
                                    "max_width": "",
                                    "max_height": "",
                                    "max_size": "",
                                    "mime_types": "",
                                    "parent_repeater": "field_637e59d26bf87"
                                },
                                {
                                    "key": "field_637e5a9a6bf8b",
                                    "label": "Form",
                                    "name": "gravity_forms",
                                    "aria-label": "",
                                    "type": "select",
                                    "instructions": "",
                                    "required": 0,
                                    "conditional_logic": 0,
                                    "wrapper": {
                                        "width": "",
                                        "class": "",
                                        "id": ""
                                    },
                                    "multiple": 0,
                                    "allow_null": 0,
                                    "choices": {
                                        "1": "Footer Newsletter",
                                        "5": "Make an Enquiry - General",
                                        "2": "Make an Enquiry - Specific Holiday",
                                        "7": "Newsletter modal form A",
                                        "8": "Newsletter modal form B (switched off in June 2023)",
                                        "9": "Newsletter signup - landing page",
                                        "3": "Payment Form"
                                    },
                                    "default_value": false,
                                    "ui": 0,
                                    "ajax": 0,
                                    "placeholder": "",
                                    "return_format": "value",
                                    "parent_repeater": "field_637e59d26bf87"
                                }
                            ]
                        }
                    ],
                    "min": "",
                    "max": ""
                }
            },
            "button_label": "Add Content",
            "min": "",
            "max": ""
        }
        {
            "key": "field_steps_ahead_global_tab",
            "label": "Steps Ahead Panel",
            "name": "",
            "aria-label": "",
            "type": "tab",
            "instructions": "",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "placement": "left",
            "endpoint": 0,
            "selected": 0
        },
        {
            "key": "field_steps_ahead_global_content",
            "label": "Steps Ahead Global Content",
            "name": "steps_ahead_global_content",
            "aria-label": "",
            "type": "group",
            "instructions": "This content will be used on all pages that have Steps Ahead panels enabled. Individual pages only control background color, curved edge, and on/off settings.",
            "required": 0,
            "conditional_logic": 0,
            "wrapper": {
                "width": "",
                "class": "",
                "id": ""
            },
            "layout": "block",
            "sub_fields": [
                {
                    "key": "field_steps_ahead_global_title",
                    "label": "Panel Title",
                    "name": "panel_title",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "Independently Owned, Personally Run",
                    "placeholder": "",
                    "prepend": "",
                    "append": "",
                    "maxlength": ""
                },
                {
                    "key": "field_steps_ahead_global_copy",
                    "label": "Panel Copy",
                    "name": "panel_copy",
                    "aria-label": "",
                    "type": "wysiwyg",
                    "instructions": "",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "",
                    "tabs": "all",
                    "toolbar": "full",
                    "media_upload": 1,
                    "delay": 0
                },
                {
                    "key": "field_steps_ahead_global_hero_image",
                    "label": "Hero Image",
                    "name": "hero_image",
                    "aria-label": "",
                    "type": "image",
                    "instructions": "Large hero image that appears at the top of the panel",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "return_format": "array",
                    "preview_size": "medium",
                    "library": "all",
                    "min_width": "",
                    "min_height": "",
                    "min_size": "",
                    "max_width": "",
                    "max_height": "",
                    "max_size": "",
                    "mime_types": ""
                },
                {
                    "key": "field_steps_ahead_global_hero_title",
                    "label": "Hero Title",
                    "name": "hero_title",
                    "aria-label": "",
                    "type": "text",
                    "instructions": "Title that overlays the hero image",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "default_value": "Independently Owned,<br>Personally Run",
                    "placeholder": "",
                    "prepend": "",
                    "append": "",
                    "maxlength": ""
                },
                {
                    "key": "field_steps_ahead_global_columns",
                    "label": "Content Columns",
                    "name": "columns",
                    "aria-label": "",
                    "type": "repeater",
                    "instructions": "Add up to 4 columns for the steps ahead panel content",
                    "required": 0,
                    "conditional_logic": 0,
                    "wrapper": {
                        "width": "",
                        "class": "",
                        "id": ""
                    },
                    "collapsed": "",
                    "min": 0,
                    "max": 4,
                    "layout": "block",
                    "button_label": "Add Column",
                    "sub_fields": [
                        {
                            "key": "field_steps_ahead_global_col_heading",
                            "label": "Heading",
                            "name": "heading",
                            "aria-label": "",
                            "type": "text",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "placeholder": "",
                            "prepend": "",
                            "append": "",
                            "maxlength": "",
                            "parent_repeater": "field_steps_ahead_global_columns"
                        },
                        {
                            "key": "field_steps_ahead_global_col_copy",
                            "label": "Copy",
                            "name": "copy",
                            "aria-label": "",
                            "type": "wysiwyg",
                            "instructions": "",
                            "required": 0,
                            "conditional_logic": 0,
                            "wrapper": {
                                "width": "",
                                "class": "",
                                "id": ""
                            },
                            "default_value": "",
                            "tabs": "all",
                            "toolbar": "full",
                            "media_upload": 1,
                            "delay": 0,
                            "parent_repeater": "field_steps_ahead_global_columns"
                        }
                    ],
                    "rows_per_page": 20
                }
            ]
        }
    ],
    "location": [
        [
            {
                "param": "options_page",
                "operator": "==",
                "value": "theme-settings"
            }
        ]
    ],
    "menu_order": 0,
    "position": "normal",
    "style": "default",
    "label_placement": "top",
    "instruction_placement": "label",
    "hide_on_screen": "",
    "active": true,
    "description": "",
    "show_in_rest": 0,
    "modified": 1734622054
}
