<?php
/**
 * Steps Ahead Panel Component
 * Displays steps ahead content on listing pages after the bottom CTA
 * Content comes from Theme Settings, individual pages control styling and enable/disable
 */

$page_obj = get_queried_object();
$panel_settings = null;

// Check if we're on a supported page type and get the appropriate settings
if (is_tax('holiday-type')) {
    // Holiday Type taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);
} elseif (is_tax('holiday-regions')) {
    // Holiday Regions taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);
} elseif (is_post_type_archive('holiday')) {
    // All Holidays archive page - get settings from options page
    $panel_settings = get_field('steps_ahead_panel', 'option');
} else {
    // Not a supported page type
    return;
}

// Check if panel is enabled
if (!$panel_settings || !$panel_settings['enable_panel']) {
    return;
}

// Get global content from Theme Settings
$global_content = get_field('steps_ahead_global_content', 'option');
if (!$global_content) {
    return;
}

// Use global content for title, copy, and columns
$panel_title = $global_content['panel_title'] ?: '';
$panel_copy = $global_content['panel_copy'] ?: '';
$hero_image = $global_content['hero_image'] ?: null;
$hero_title = $global_content['hero_title'] ?: '';
$panel_columns = $global_content['columns'];

// Use individual page settings for styling
$background_color = $panel_settings['background_color'] ?: '#eaf2f1'; // Default to same as old cta-bottom
$curved_edge = $panel_settings['curved_edge'] ?: 'bottom'; // Default to bottom curve

if (!$panel_columns || empty($panel_columns)) {
    return;
}

// Set up background classes
$background_classes = 'has-background';
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
}

?>

<section class="steps-ahead steps-ahead--listing">
    <?php if($hero_image) : ?>
        <div class="steps-ahead__hero">
            <div class="steps-ahead__hero-image">
                <img src="<?php echo $hero_image['url']; ?>" alt="<?php echo $hero_image['alt']; ?>">
            </div>
            <?php if($hero_title) : ?>
                <div class="steps-ahead__hero-content">
                    <h2 class="steps-ahead__hero-title"><?php echo $hero_title; ?></h2>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="steps-ahead__inner<?php echo $background_classes ? ' ' . $background_classes : ''; ?>" data-aos="fade" style="background-color: <?php echo $background_color; ?>;">
        <div class="container steps-ahead__container">
            <?php if($panel_title && !$hero_image) : ?>
                <div class="steps-ahead__content centre inner-container">
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($panel_title); ?></h2>
                    <?php if($panel_copy) : ?>
                        <div class="content-area steps-ahead__copy">
                            <?php echo $panel_copy; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if($panel_columns) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($panel_columns as $column) : ?>
                        <?php
                        $cheading = $column['heading'];
                        $ccopy = $column['copy'];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre">
                                <?php if($cheading) : ?>
                                    <h4 class="steps-ahead__col-heading"><?php echo esc_html($cheading); ?></h4>
                                <?php endif; ?>
                                <?php if($ccopy) : ?>
                                    <div class="steps-ahead__col-copy content-area">
                                        <?php echo $ccopy; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->
