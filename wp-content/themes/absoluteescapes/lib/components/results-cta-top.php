<!-- Call To Action -->
<?php 
 
 $defaults = [
    'status' => true,
    'title' => 'Talk to our travel experts',
    'text' => 'Our specialists combine first-hand experience with meticulous planning to craft holidays unique to you. Enquire today to plan your perfect escape.',
    'button_label' => 'Make an Enquiry',
    'button_link' => '/general-enquiry/',
    'icon' => '<svg class="svg-inline--fa fa-phone fa-w-16" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="phone" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"></path></svg>',
    'contact_number' => '+44 (0) ************',
    'use_image' => true,
    'image' => ['url' => '/wp-content/themes/absoluteescapes/dist/img/holiday-listing-1-top-panel.jpg', 'alt' => 'Alt text'],
 ];
 $term = get_queried_object();
 $top_cta = get_field('top_cta', $term );

 // Debug and fix the data type issue
 if (!isset($top_cta) || !is_array($top_cta)) {
     $top_cta = $defaults;
 }

 // Ensure status is properly set
 if (!isset($top_cta['status'])) {
     $top_cta['status'] = true;
 }

 if ($top_cta['status'] === true) { ?>
<div class="listing-cta cta-top">
    <div class="listing-cta__background">
        <div class="container">
            <div class="listing-cta__content">
               <?php if ($top_cta['use_image'] === true) { ?>
                <div class="listing-cta__image">
                        <?php if (empty($top_cta['image'])) { $top_cta['image'] = $defaults['image']; } ?>
                           <img loading="lazy" src="<?php echo $top_cta['image']['url'] ?>" alt="<?php echo $top_cta['image']['alt'] ?>">    
                </div>
                <?php } ?>
                <div class="listing-cta__text<?php if ($top_cta['use_image'] !== true) { echo ' centre'; } ?>">
                    <?php if (!empty($top_cta['title'])) { ?>
                        <h2 class="text-block__heading heading-light text-weight-regular"><?php echo $top_cta['title']; ?></h2>
                    <?php } ?>
                    <?php if (!empty($top_cta['text'])) { ?>
                        <p><?php echo $top_cta['text']; ?></p>
                    <?php } ?>
                    <?php if ((!empty($top_cta['button_link']) && !empty($top_cta['button_label'])) || !empty($top_cta['icon']) || !empty($top_cta['contact_number'])) { ?>
                        <p class="listing-cta__actions">
                    <?php } ?>
                    <?php if (!empty($top_cta['button_link']) && !empty($top_cta['button_label'])) { ?>
                     <a href="<?php echo $top_cta['button_link']; ?>" class="button"><?php echo $top_cta['button_label']; ?></a>
                     <?php } ?>
                     <?php if (!empty($top_cta['contact_number'])) { ?>
                      <a href="tel:<?php echo preg_replace('/[^0-9\+]/', '', $top_cta['contact_number']); ?>">
                        <?php if (!empty($top_cta['icon'])) { ?>
                            <?php echo $top_cta['icon']; ?>
                        <?php } ?>
                        <strong><?php echo $top_cta['contact_number']; ?></strong>
                       </a>
                     <?php } ?>
                     <?php if ((!empty($top_cta['button_link']) && !empty($top_cta['button_label'])) || !empty($top_cta['icon']) || !empty($top_cta['contact_number'])) { ?>
                     </p>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php } ?>
<!-- / Call To Action -->